import { TableColumnsType } from 'antd';
import { ColumnTypes, EditColumns } from './editTable';
import { TCustomer } from '../../../types/trainingUserCreate';

export const columnsTrainingUser: TableColumnsType = [
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    width: 140,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    key: 'phone',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    width: 150,

    render: (value: string[]) => (value ? value : '-'),
  },
];

export const ARRAY_FIELD_CREATE_TRAINING_USER = ['name', 'phone', 'email'];

export const columnCreate: (ColumnTypes<TCustomer>[number] & EditColumns)[] = [
  {
    title: 'Tên khách hàng',
    dataIndex: 'name',
    key: 'name',
    editable: true,
    align: 'center',
    width: '30%',
    inputType: 'text',
    inputProps: { maxLength: 60, placeholder: 'Nhập tên khách hàng' },
    rules: () => [
      {
        validator: async (_, value) => {
          if (value === undefined || value === null || value.trim() === '') {
            return Promise.reject('Vui lòng nhập tên khách hàng');
          }
          return Promise.resolve();
        },
        required: true,
        message: 'Vui lòng nhập tên khách hàng',
      },
    ],
  },
  {
    title: 'Số điện thoại',
    dataIndex: 'phone',
    key: 'phone',
    editable: true,
    align: 'center',
    width: '30%',
    inputType: 'number',
    inputProps: { maxLength: 15, placeholder: 'Nhập số điện thoại khách hàng	' },
    rules: () => [
      {
        validator: async (_, value) => {
          if (value === undefined || value === null || value.trim() === '') {
            return Promise.reject('Vui lòng nhập số điện thoại');
          }
          return Promise.resolve();
        },
        required: true,
        message: 'Vui lòng nhập số điện thoại',
      },
    ],
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email',
    editable: true,
    align: 'center',
    width: '25%',
    inputType: 'text',
    inputProps: { placeholder: 'Nhập email' },

    // rules: (record: unknown) => [
    //   {
    //     validator: async (_, value) => {
    //       if (value === undefined || value === null || value.trim() === '') {
    //         return Promise.reject('Vui lòng nhập Email');
    //       }
    //       return Promise.resolve();
    //     },
    //     required: !(record as TCustomer)?.unRequiredEmailRegister,
    //     message: 'Vui lòng nhập Email',
    //   },
    // ],
  },
];
