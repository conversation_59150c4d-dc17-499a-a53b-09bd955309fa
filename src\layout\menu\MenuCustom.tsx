import { DownOutlined, SearchOutlined, UpOutlined } from '@ant-design/icons';
import { Flex, Input, Menu } from 'antd';
import { memo, useEffect, useMemo, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import './MenuCustom.scss';
import { menuItems } from './MenuItem';
// import { useAuth } from '../../context/AuthContext';

const MenuCustom: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  // tạm thời bỏ kiểm tra quyền để dev
  // const { hasAuthority } = useAuth();

  // lọc các menu có quyền và theo search
  const { filteredMenuData, defaultOpenKeys } = useMemo(() => {
    const searchLower = searchTerm.toLowerCase();

    const filteredMenuData = menuItems.reduce(
      (acc, menuItem) => {
        const filteredChildren = menuItem.children.filter(
          child => child.label.toLowerCase().includes(searchLower),
          // tạm thời bỏ kiểm tra quyền
          // && hasAuthority(child.permission || []), // tạm thời
        );

        const filteredGroups = menuItem.groups
          .map(group => ({
            ...group,
            children: group.children.filter(
              item => item.label.toLowerCase().includes(searchLower),
              // tạm thời bỏ kiểm tra quyền
              // && hasAuthority(item.permission || []),
            ),
          }))
          .filter(group => group.children.length > 0);

        if (filteredChildren.length || filteredGroups.length) {
          acc.push({ ...menuItem, children: filteredChildren, groups: filteredGroups });
        }

        return acc;
      },
      [] as typeof menuItems,
    );

    const defaultOpenKeys = filteredMenuData.map(menuItem => menuItem.key);

    return { filteredMenuData, defaultOpenKeys };
  }, [searchTerm]);

  // State lưu danh sách menu dang mở
  const [openKeys, setOpenKeys] = useState<string[]>(defaultOpenKeys);

  // Cập nhật openKeys khi defaultOpenKeys thay đổi.
  useEffect(() => {
    setOpenKeys(defaultOpenKeys);
  }, [defaultOpenKeys]);

  // handle mở/đóng menu
  const handleOpenChange = (keys: string[]) => {
    setOpenKeys(keys);
  };

  // Xây dựng danh sách items cho Menu
  const items = filteredMenuData.length
    ? filteredMenuData.map(menuItem => ({
        key: menuItem.key,
        icon: menuItem.icon,
        label: menuItem.label,
        children: [
          ...menuItem.children.map(child => ({
            key: child.path || child.key,
            label: child.label,
            onClick: () => (child?.isNewTab ? window.open(child.path || '', '_blank') : navigate(child.path || '')),
          })),
          ...menuItem.groups.map(group => ({
            key: group.key,
            type: 'group',
            label: group.label,
            children: group.children.map(item => ({
              key: item.path || item.key,
              label: item.label,
              onClick: () => (item?.isNewTab ? window.open(item.path || '', '_blank') : navigate(item.path || '')),
            })),
          })),
        ],
      }))
    : [{ key: 'no-results', label: 'Không tìm thấy' }];

  return (
    <>
      <Flex className="header-sider-search" justify="flex-start" align="center">
        <Input
          placeholder="Tìm kiếm"
          onChange={e => setSearchTerm(e.target.value)}
          suffix={<SearchOutlined style={{ color: 'rgba(0, 0, 0, 0.25)' }} />}
        />
      </Flex>
      <Menu
        className={`container-menu ${filteredMenuData.length === 0 ? 'no-results' : ''}`}
        mode="inline"
        openKeys={openKeys}
        onOpenChange={handleOpenChange}
        expandIcon={({ isOpen }) =>
          isOpen ? <UpOutlined className="icon-subMenu" /> : <DownOutlined className="icon-subMenu" />
        }
        items={items}
        selectedKeys={[location.pathname]}
      />
    </>
  );
};

export default memo(MenuCustom);
