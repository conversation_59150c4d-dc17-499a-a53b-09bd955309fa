import { AddressObject, DetailProject, Project } from '../project/project';

export interface BookingTicket {
  id?: string;
  amountRegistration?: number;
  status?: string;
  customer?: {
    personalInfo?: {
      email?: string;
      phone?: string;
      name?: string;
      identities?: Identities[];
    };
    identities?: Identities[];
    info?: { address?: AddressObject; rootAddress?: AddressObject };
    code?: string;
    type?: string;
    company: {
      name?: string;
      address?: AddressObject;
    };
    taxCode?: string;
  };
  propertyUnit?: {
    code?: string;
  };
  bookingTicketCode?: string;
  employee?: Employee;
  code?: string;
  reciept?: Reciept[];
  createdDate?: string;
  project?: Project;
  amount?: number;
  productCode?: string;
  escrowTicketCode?: string;
}

export interface Reciept {
  id?: string;
  amount: number;
  code?: string;
  status: string;
  type?: string;
  receiptNum?: string;
  receiptDate?: string;
  name?: string;
}

export interface Identities {
  _id?: string;
  type?: string;
  value?: string;
  date?: string;
  place?: string;
}

export interface DuplicateCustomer {
  loanType?: string;
  name?: string;
  code?: string;
  type?: string;
  customerType?: string;
  identityType?: string;
  identityNo?: string;
  target?: string;
  bpID?: string;
  personalInfo?: PersonalInfo;
  employee?: Employee;
  identities?: Identities;
  isActive?: number;
  description?: string;
  takeNote?: string;
  taxCode?: string;
  status?: number;
  syncErpData?: string;
  company?: Company;
  softDelete?: boolean;
  softDeleteReason?: string;
  updatedBy?: string;
  createdBy?: string;
  _id?: string;
  info?: Info;
  bankInfo?: [
    {
      beneficiary?: string;
      code?: string;
      name?: string;
      branchCode?: string;
      accountNumber?: string;
      beneciary?: string;
    },
  ];
  id?: string;
  pos?: {
    staffIds?: string[];
    parentId?: string;
    name?: string;
    code?: string;
    type?: string;
    id?: string;
    managerId?: string;
  };
  createdDate?: string;
  updatedDate?: string;
  bpId?: string;
  isTakeCare?: boolean;
}

export interface DuplicateResp {
  isDuplicate?: boolean;
  customerType?: string;
  data?: DuplicateCustomer[];
  type?: string;
  employeeTakeCare?: Employee;
  bookingTicketCode?: { id: string; bookingTicketCode: string; escrowTicketCode: string };
}

export interface FormVerify {
  type?: string;
  identityType?: string;
  identityNo?: string;
  issueDate?: string;
  issueLocation?: string;
  taxCode?: string;
  employeeTakeCare?: Employee;
  bpID?: string;
}

export interface Bank {
  name: string;
  code: string;
  bankCode?: string;
  bankName?: string;
  accountNumber?: string;
  beneciary?: string;
}

export interface BankOption {
  value: string;
  label: string;
  originalBankCode: string;
}

export interface BankAccount {
  beneciary?: string;
  bankName?: string;
  bankCode?: string;
  name?: string;
  code?: string;
  accountNumber?: string;
  beneficiary?: string;
}

export interface PropertyUnit {
  price?: string;
  priceVat?: string;
  priceAbove?: string;
  priceAboveVat?: string;
  housePriceVat?: string;
  landPriceVat?: number;
  housePrice?: number;
  landPrice?: number;
  area?: string;
  bedroom?: string;
  primaryStatus?: string;
  code?: string;
  shortCode?: string;
  status?: string;
  direction?: string;
  pos?: string;
  project?: {
    id?: string;
    name?: string;
  };
  source?: string;
  createdDate?: string;
  id?: string;
  modifiedDate?: string;
  attributes?: [];
  stage?: number;
  priorities?: [];
  extendable?: boolean;
  extendPos?: string;
  publishPrice?: boolean;
  block?: string;
  floor?: string;
  description?: string;
  owner?: {};
  outsideArea?: string;
  contractPrice?: number;
  contractPriceForMaintenanceFee?: number;
  colorSyncErp?: string;
  lstImage?: [];
  image360?: string;
  corner?: string;
  insideArea?: string;
  constructStatus?: string;
  constructStatusXD?: string;
  salesProgram?: {
    id?: string;
    name?: string;
  };
}

export interface UpdateCustomer {
  id?: string;
  personalInfo?: PersonalInfo;
  info?: Info;
  bankInfo?: BankAccount[];
  takeNote?: string;
  company?: Company;
}

export interface UpdateDemand {
  id?: string;
  type?: string;
  continueUpdate?: boolean;
  company?: Company;
  info?: Info;
  bankInfo?: BankAccount[];
  personalInfo?: PersonalInfo;
}

export interface CreateDemandIndividual {
  type?: string;
  name?: string;
  phone?: string;
  gender?: number;
  leadCode?: string;
  continueCreate?: boolean;
  bankInfo?: BankAccount[];
  info?: Info;
  personalInfo?: PersonalInfo;
}

export interface CreateDemandBusiness {
  type?: string;
  name?: string;
  phone?: string;
  leadCode?: string;
  continueCreate?: boolean;
  taxCode?: string;
  companyName?: string;
  company?: Company;
  bankInfo?: BankAccount[];
  info?: Info;
  personalInfo?: PersonalInfo;
}

export interface CreateBookingTicket {
  id?: string;
  ticketType?: string;
  employeeTakeCareId?: string;
  projectId?: string;
  salesProgramId?: string;
  demandPropertyId?: string;
  customer?: {
    useResidentialAddress?: boolean;
    code?: string;
    name?: string;
    gender?: number;
    onlyYear?: string;
    birthday?: string;
    birthdayYear?: string;
    phone?: string;
    email?: string;
    identityType?: string;
    identityNumber?: string;
    identityIssueDate?: string;
    identityIssueLocation?: string;
    rootAddress?: AddressObject;
    address?: AddressObject;
    taxCode?: string;
    bankInfo?: {
      name?: string;
      accountNumber?: string;
      beneciary?: string;
    };
    company?: Company;
    position?: string;
    type?: string;
    mainBank?: {
      name?: string;
      accountNumber?: string;
      beneciary?: string;
    };
    bpID?: string;
  };
  files?: File[];
  demandCategory?: string;
  amountRegistration?: string;
  note?: string;
  bookingTicketCode?: string;
  isCreateAtStage2?: boolean;
  status?: string;
  escrowTicketCode?: string;
  loanType?: boolean;
}

export interface BookingTicketInfo {
  _id?: string;
  project?: DetailProject;
  status?: string;
  stage?: number;
  priority?: number;
  isPicked?: boolean;
  emailConfirm?: boolean;
  sentEmail?: boolean;
  sentEmailDate?: string;
  description?: string;
  active?: boolean;
  softDelete?: boolean;
  surveys?: [];
  unCheckAmount?: boolean;
  syncErpCancel?: boolean;
  isRefund?: boolean;
  ticketType?: string;
  propertyUnitId?: string;
  customer?: Customer;
  files?: File[];
  amountRegistration?: number;
  note?: string;
  demandCategory?: string;
  bookingTicketCode?: string;
  createdBy?: string;
  employee?: Employee;
  code?: string;
  notiUser?: {
    id?: string;
  };
  id?: string;
  documents?: [];
  reciept?: [];
  modifiedDate?: string;
  createdDate?: string;
  historyStatus?: [{ date?: string; status?: string }];
  salesProgramId?: string;
  salesProgram?: SalesProgram;
  propertyUnit?: PropertyUnit;
  __v?: number;
  escrowTicketCode?: string;
  employeeTakeCareCode?: string;
  loanType?: boolean;
}

export interface File {
  uid: string;
  _id?: string;
  name?: string;
  url?: string;
  absoluteUrl?: string;
  uploadName?: string;
}

export interface Employee {
  accountId?: string;
  orgName?: string;
  orgCode?: string;
  staffIds?: string[];
  workingAt?: string;
  managerAt?: string;
  pos?: {
    staffIds?: string[];
    parentId?: string;
    name?: string;
    code?: string;
    type?: string;
    id?: string;
    managerId?: string;
  };
  id?: string;
  code?: string;
  email?: string;
  erp?: {
    code?: string;
    url?: string;
  };
  images?: {
    list?: [];
    zones?: [];
    videos?: [
      {
        url?: string;
      },
    ];
  };
  name?: string;
  phone?: string;
}

export interface SaleProgram {
  id?: string;
  project?: {
    id?: string;
    name?: string;
  };
  code?: string;
  name?: string;
}

export interface SalesProgram {
  id: string;
  name: string;
  code: string;
  allowBookingPriority: boolean;
}
export interface PersonalInfo {
  email?: string;
  name?: string;
  shortName?: string;
  phone?: string;
  relationshipStatus?: string;
  position?: string;
  job?: string;
  income?: string;
  incomeSource?: string;
  identities?: Identities[];
}

export interface Info {
  gender?: number;
  age?: string;
  onlyYear?: boolean;
  birthday?: string;
  birthdayYear?: string;
  address?: AddressObject;
  cloneAddress?: boolean;
  rootAddress?: AddressObject;
  useResidentialAddress?: boolean;
}

export interface Company {
  name: string;
  shortName: string;
  issueDate: string;
  issueLocation: string;
  address: AddressObject;
}

export interface Customer {
  info: Info;
  code: string;
  mainBank: {
    name?: string;
    accountNumber?: string;
    beneciary?: string;
    bankCode?: string;
  };
  bankInfo: BankAccount[];
  personalInfo: PersonalInfo;
  employee: Employee;
  identities: Identities[];
  identityType: string;
  identityNumber: string;
  identityDate: string;
  identityPlace: string;
  taxCode: string;
  company: Company;
  position: string;
  type: string;
}

export interface CreateUrlPaymentResponse {
  data: {
    data: {
      amount: number;
      transactionId: string;
      bankCode: string;
      expiredDate: string;
      transactionType: string;
      response: {
        code: string;
        data: string;
      };
      status: string;
      link: string;
      createdBy: string;
      modifiedBy: string;
      id: string;
      softDelete: boolean;
      createdDate: string;
      modifiedDate: string;
      _id: string;
      __v: number;
    };
  };
}
