import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Form, Input, Select, DatePicker, Checkbox, Button, Row, Col, Radio, UploadFile, Typography } from 'antd';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useCreateField, useFetch } from '../../../../../hooks';
import { Bank, BankAccount, BankOption, CreateBookingTicket, DuplicateResp } from '../../../../../types/bookingRequest';
import { getBanks } from '../../../../../service/bank';
import ModalComponent from '../../../../../components/modal';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import dayjs from 'dayjs';
import { FORMAT_DATE, OPTIONS_GENDER } from '../../../../../constants/common';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import { createBookingTicket } from '../../../../../service/bookingTicket';
import { useParams } from 'react-router-dom';
import { getProvinces } from '../../../../../service/address';
import UploadFileBookingTicket from '../../../bookingTicketPersonal/components/UploadFileBookingTicket';
import { useSellProgramStore } from '../../../../Store';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

const REGEX_PHONE_VN = /^0\d{9,14}$/;

interface CreateBookingRequestStep3Props {
  visible: boolean;
  onCancel: (hasFormChanged?: boolean, resetForm?: () => void) => void;
  onSave: (values: unknown) => void;
  initialData?: DuplicateResp;
  duplicateData?: DuplicateResp | null;
  selectedPropertyId?: string;
  step1Data?: any;
}

const CreateBookingRequestStep3: React.FC<CreateBookingRequestStep3Props> = ({
  visible,
  onCancel,
  onSave,
  initialData,
  duplicateData,
  selectedPropertyId,
  step1Data,
}) => {
  const { id: projectId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const issueDate = Form.useWatch('issueDate', form);
  const issueLocation = Form.useWatch('issueLocation', form);
  const address = Form.useWatch(['address'], form);
  const rootAddress = Form.useWatch(['rootAddress'], form);
  const type = Form.useWatch(['type'], form);
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];

  const isKHCT = duplicateData?.customerType === 'customer';

  // Local state
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [yearOnly, setYearOnly] = useState(false);
  const [isModified, setIsModified] = useState(false);

  // API calls
  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });

  const { mutateAsync: _createBookingTicket, isPending: isCreatingBookingTicket } = useCreateField<CreateBookingTicket>(
    {
      apiQuery: createBookingTicket,
      keyOfListQuery: ['get-list-payment-transaction', projectId, salesProgramIds],
      keyOfDetailQuery: ['getPropertyUnitByProjectIdAndSalesProgramIds', projectId, salesProgramIds],
      isMessageError: false,
    },
  );

  const provinces = dataProvinces?.data?.data;
  const banks = dataBanks?.data?.data || [];

  // Tính toán options cho bank từ bankInfoOptions - tối ưu hóa
  const bankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );

  // Tối ưu hóa bank handlers
  const bankHandlers = useMemo(
    () => ({
      // Xử lý khi chọn tài khoản ngân hàng mặc định
      selectMainBank: (value: string) => {
        const selectedOption = bankOptions.find(option => option.value === value);
        const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);

        form.setFieldsValue({
          mainBankId: selectedBank ? value : undefined,
          mainBank: selectedBank
            ? {
                name: selectedBank.bankName || '',
                accountNumber: selectedBank.accountNumber || '',
                beneciary: selectedBank.beneciary || '',
              }
            : { name: '', accountNumber: '', beneciary: '' },
        });
      },

      // Xử lý xóa tài khoản ngân hàng
      removeBankAccount: (name: number) => {
        const currentMainBankId = form.getFieldValue('mainBankId');
        const bankAccount = form.getFieldValue('bankAccount');
        if (!bankAccount || !currentMainBankId) return;

        const [bankCode, currentIndex] = currentMainBankId.split('-');
        const currentIndexNum = parseInt(currentIndex);

        // Reset main bank nếu đang xóa tài khoản chính
        if (currentIndexNum === name) {
          form.setFieldsValue({
            mainBankId: undefined,
            mainBank: { name: '', accountNumber: '', beneciary: '' },
          });
        }
        // Cập nhật index nếu xóa tài khoản phía trước
        else if (currentIndexNum > name) {
          form.setFieldsValue({
            mainBankId: `${bankCode}-${currentIndexNum - 1}`,
          });
        }
      },

      // Xử lý thay đổi ngân hàng trong form list
      changeBankInList: (fieldIndex: number, value: string, option: any) => {
        form.setFieldsValue({
          bankAccount: {
            [fieldIndex]: {
              bankCode: value,
              bankName: Array.isArray(option) ? '' : option?.label || '',
              accountNumber: form.getFieldValue(['bankAccount', fieldIndex, 'accountNumber']) || '',
              beneciary: form.getFieldValue(['bankAccount', fieldIndex, 'beneciary']) || '',
            },
          },
        });
      },

      // Validator cho bank fields
      validateBankField: (fieldIndex: number, fieldName: string) => (_: unknown, value: string) => {
        const bankCode = form.getFieldValue(['bankAccount', fieldIndex, 'bankCode']);
        if (bankCode && !value?.trim()) {
          return Promise.reject(`Vui lòng nhập ${fieldName}`);
        }
        return Promise.resolve();
      },
    }),
    [bankOptions, bankInfoOptions, form],
  );

  // Handler khi lưu form
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const { mainBank } = values;

      let identityIssueLocationValue: string = '';

      if (typeof values?.issueLocation === 'object' && values?.issueLocation?.value) {
        const selectedProvince = provinces?.find(p => p.code === values.issueLocation.value);
        identityIssueLocationValue = selectedProvince?.nameVN || values.issueLocation.label || '';
      } else if (typeof values?.issueLocation === 'string') {
        const selectedProvince = provinces?.find(p => p.code === values.issueLocation);
        identityIssueLocationValue = selectedProvince?.nameVN || values.issueLocation;
      } else {
        identityIssueLocationValue = values?.issueLocation || '';
      }

      const payloadCreateTicket: CreateBookingTicket = {
        projectId: projectId,
        ticketType: 'YCDC',
        employeeTakeCareId: step1Data?.employeeTakeCareId?.accountId || '',
        id: initialData?.bookingTicketCode?.id || '',
        demandPropertyId: selectedPropertyId || '',
        bookingTicketCode: initialData?.bookingTicketCode?.bookingTicketCode || '',
        escrowTicketCode: initialData?.bookingTicketCode?.escrowTicketCode || '',
        customer: {
          useResidentialAddress: values?.info?.cloneAddress,
          code: values?.code,
          gender: values?.info?.gender,
          onlyYear: values?.info?.onlyYear,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly && values?.info?.birthdayYear ? dayjs(values?.info?.birthdayYear).format('YYYY') : '',
          name: values.personalInfo?.name,
          phone: values.personalInfo?.phone,
          email: values.personalInfo?.email || undefined,
          identityType: values?.identityType,
          identityNumber: values?.type === 'business' ? values?.taxCode : values?.identityNo,
          identityIssueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          identityIssueLocation: identityIssueLocationValue,
          address: { ...values?.address },
          taxCode: values?.taxCode,
          bankInfo: bankInfoOptions,
          mainBank: {
            name: mainBank?.name || '',
            accountNumber: mainBank?.accountNumber || '',
            beneciary: mainBank?.beneciary || '',
          },
          position: values?.position,
          type: values?.type,
          rootAddress: { ...values.rootAddress },
          company: {
            ...values?.company,
            issueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
            issueLocation: values?.issueLocation,
            address: values?.companyAddress,
          },
          bpID: step1Data?.bpID,
        },
        files: fileList.map(file => ({
          uid: file.uid || '',
          name: file.name,
          url: file.key || '',
          absoluteUrl: file?.absoluteUrl || '',
          uploadName: file.name,
        })),
        amountRegistration: values?.registrationAmount,
        note: values?.description,
        isCreateAtStage2: true,
        status: initialData?.bookingTicketCode?.bookingTicketCode ? 'CLOSE' : 'PROCESSING_LOCK',
        loanType: values?.loanType,
      };
      const ticketResp = await _createBookingTicket(payloadCreateTicket);
      if (ticketResp?.data?.statusCode === '0') {
        onSave(payloadCreateTicket);
      }
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  // Đánh dấu form đã thay đổi
  const validateForm = () => setIsModified(true);

  // Xử lý khi chọn sao chép địa chỉ
  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  // Component Bank Account List - tối ưu hóa
  const BankAccountList = useMemo(
    () => (
      <Form.List name="bankAccount">
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <Row align="middle" gutter={[8, 8]} key={key}>
                <Col span={10}>
                  <Form.Item {...restField} name={[name, 'bankName']} label="Ngân hàng" rules={[{ required: false }]}>
                    <Select
                      placeholder="Chọn ngân hàng"
                      allowClear
                      showSearch
                      loading={isLoadingBanks}
                      options={banks.map(item => ({
                        value: item?.bankCode,
                        label: item?.bankName,
                      }))}
                      onChange={(value, option) => bankHandlers.changeBankInList(name, value, option)}
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    {...restField}
                    name={[name, 'accountNumber']}
                    label="Số tài khoản"
                    rules={[{ validator: bankHandlers.validateBankField(name, 'số tài khoản') }]}
                  >
                    <Input
                      placeholder="Nhập số tài khoản"
                      maxLength={20}
                      onBlur={e => {
                        const fieldPath = ['bankAccount', name, 'accountNumber'];
                        form.setFieldValue(fieldPath, e.target.value.trim());
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={7}>
                  <Form.Item
                    {...restField}
                    name={[name, 'beneciary']}
                    label="Tên người thụ hưởng"
                    rules={[{ validator: bankHandlers.validateBankField(name, 'tên người thụ hưởng') }]}
                  >
                    <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} />
                  </Form.Item>
                </Col>
                <Col span={1}>
                  <CloseOutlined
                    style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                    onClick={() => {
                      remove(name);
                      bankHandlers.removeBankAccount(name);
                    }}
                  />
                </Col>
              </Row>
            ))}

            {fields.length < 10 && (
              <Col span={23}>
                <Button
                  type="dashed"
                  onClick={() => add({ bankCode: undefined, bankName: '', accountNumber: '', beneciary: '' })}
                  style={{ padding: 0 }}
                  block
                  icon={<PlusOutlined />}
                >
                  Thêm tài khoản giao dịch
                </Button>
              </Col>
            )}
          </>
        )}
      </Form.List>
    ),
    [banks, isLoadingBanks, bankHandlers, form],
  );

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }

    if (step1Data) {
      const bookingTicketCode =
        step1Data?.bookingTicketCode?.ticketType === 'YCDC'
          ? step1Data?.bookingTicketCode?.escrowTicketCode
          : step1Data?.bookingTicketCode?.bookingTicketCode;
      form.setFieldsValue({
        employeeTakeCareId: step1Data.employeeTakeCareId
          ? `${step1Data.employeeTakeCareId?.code} - ${step1Data.employeeTakeCareId?.name}`
          : undefined,
        bookingTicketCode: bookingTicketCode,
        bankBookingTicket: `${step1Data?.bookingTicketCode?.customer?.mainBank?.name} - ${step1Data?.bookingTicketCode?.customer?.mainBank?.accountNumber} - ${step1Data?.bookingTicketCode?.customer?.mainBank?.beneciary}`,
      });
    }

    if (duplicateData?.data?.length) {
      const customerData = duplicateData.data[0];
      const identity = customerData.personalInfo?.identities?.[0];
      const bankInfo = customerData.bankInfo || [];
      const mainBankInfo = bankInfo[0] || {};

      form.setFieldsValue({
        type: customerData.type,
        code: customerData.code,
        identityType: customerData?.type === 'business' ? 'MST' : identity?.type,
        identityNo: identity?.value,
        issueDate:
          customerData.type === 'business'
            ? customerData?.company?.issueDate
              ? dayjs(customerData?.company?.issueDate, FORMAT_DATE)
              : null
            : identity?.date
              ? dayjs(identity?.date, FORMAT_DATE)
              : null,
        issueLocation: customerData.type === 'business' ? customerData?.company?.issueLocation : identity?.place,
        address: customerData.info?.address,
        rootAddress: customerData.info?.rootAddress,
        company: {
          name: customerData.company?.name,
        },
        personalInfo: {
          name: customerData.personalInfo?.name,
          phone: customerData.personalInfo?.phone,
          email: customerData.personalInfo?.email,
        },
        info: {
          birthday: customerData?.info?.birthday ? dayjs(customerData.info.birthday, FORMAT_DATE) : null,
          birthdayYear: customerData?.info?.birthdayYear ? dayjs(customerData.info.birthdayYear, 'YYYY') : null,
          gender: customerData.info?.gender,
          onlyYear: customerData?.info?.onlyYear,
          cloneAddress: customerData.info?.cloneAddress,
        },
        bankAccount: bankInfo.map((bank: BankAccount) => ({
          bankName: bank.name || '',
          bankCode: bank.code || '',
          accountNumber: bank.accountNumber || '',
          beneciary: bank.beneficiary || '',
        })),
        mainBank: {
          name: mainBankInfo.name || '',
          accountNumber: mainBankInfo.accountNumber || '',
          beneciary: mainBankInfo?.beneficiary || '',
        },
        description: customerData.description,
        loanType: customerData?.loanType || 'no',
      });

      setYearOnly(!!customerData.info?.onlyYear);
    }
  }, [duplicateData, form, initialData, step1Data]);

  // Kiểm tra tài khoản ngân hàng chính có hợp lệ
  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (!currentMainBankId) return;

    const [bankCode, index] = currentMainBankId.split('-');
    const bankAccount = form.getFieldValue('bankAccount') || [];
    const selectedBank = bankAccount[parseInt(index)];

    // Nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp thì reset
    if (!selectedBank || selectedBank.bankCode !== bankCode) {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: { name: '', accountNumber: '', beneciary: '' },
      });
    }
  }, [bankInfoOptions, form]);

  // Xử lý đồng bộ địa chỉ khi checkbox cloneAddress được chọn
  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  // Xử lý cảnh báo khi thoát trang mà form đã thay đổi
  useEffect(() => {
    if (!isModified) return;

    const handleBeforeunload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = '';
    };

    window.addEventListener('beforeunload', handleBeforeunload);
    return () => window.removeEventListener('beforeunload', handleBeforeunload);
  }, [isModified]);

  // Xử lý đóng modal
  const handleCancel = () => {
    onCancel(form.isFieldsTouched(), () => form.resetFields());
  };

  return (
    <ModalComponent
      title="Tạo mới yêu cầu đặt cọc 3/3"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="save" type="primary" onClick={handleSave} loading={isCreatingBookingTicket}>
          Lưu
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" initialValues={initialData} onValuesChange={validateForm}>
        <Row gutter={123}>
          <Col span={16}>
            <Title level={5}>Thông tin nhân viên</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
                  <Select placeholder="Chọn nhân viên chăm sóc" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Mã YCDCHO/YCDCO" name="bookingTicketCode">
                  <Select placeholder="Chọn mã YCDCHO/YCDCO" disabled />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="Hộ chiếu">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNo'}
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập số giấy tờ'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập số giấy tờ"
                    maxLength={60}
                    disabled={isKHCT}
                    onBlur={e => {
                      form.setFieldsValue({ taxCode: e.target.value.trim() });
                      form.setFieldsValue({ identityNo: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="issueDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker
                    placeholder="Chọn ngày cấp"
                    format={FORMAT_DATE}
                    disabled={isKHCT && issueDate != null}
                    disabledDate={current => current && current > dayjs().endOf('day')}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Nơi cấp"
                  name="issueLocation"
                  rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                >
                  <Select
                    filterOption={(input, option) =>
                      typeof option?.label === 'string'
                        ? option.label.toLowerCase().includes(input.toLowerCase())
                        : false
                    }
                    allowClear
                    options={provinces?.map(item => ({
                      value: item.code,
                      label: item.nameVN,
                    }))}
                    showSearch
                    placeholder="Chọn nơi cấp"
                    onChange={value => {
                      form.setFieldsValue({
                        issueLocation: value || undefined,
                      });
                    }}
                    disabled={isKHCT && issueLocation != null}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                    },
                    {
                      validator(_, value) {
                        if (value && value.trim() === '') {
                          return Promise.reject('Không được để khoảng trống');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    disabled={isKHCT}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase();
                    }}
                    onBlur={e => {
                      const fieldPath = type === 'business' ? ['company', 'name'] : ['personalInfo', 'name'];
                      form.setFieldValue(fieldPath, e.target.value.trim());
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã khách hàng" name="code">
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện' }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      disabled={isKHCT}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase();
                      }}
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label="Số điện thoại"
                  name={['personalInfo', 'phone']}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số điện thoại',
                    },
                    {
                      pattern: REGEX_PHONE_VN,
                      message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 10 đến 15 chữ số',
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập số điện thoại"
                    maxLength={15}
                    onKeyDown={handleKeyDownEnterNumber}
                    disabled={isKHCT}
                  />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              <Col span={14}>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>

              <Col span={10}>
                <Form.Item
                  name={['info', 'gender']}
                  label="Giới tính"
                  rules={[{ required: true, message: 'Vui lòng chọn giới tính' }]}
                  layout="horizontal"
                >
                  <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ công ty */}
            {type === 'business' && (
              <>
                <Title level={5}>Địa chỉ công ty</Title>
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item label="Địa chỉ" name={'companyAddress'} className="input-address">
                      <SelectAddress
                        placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                        parentName={'companyAddress'}
                        address={form.getFieldValue('companyAddress')}
                        handleAddressChange={validateForm}
                        isDisable={isKHCT}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="address">
                    <Form.Item name={['companyAddress', 'address']}>
                      <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled={isKHCT} />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Địa chỉ thường trú */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ thường trú</Title>
            )}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    handleAddressChange={validateForm}
                    isDisable={isKHCT}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled={isKHCT} />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ liên lạc</Title>
            )}
            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox onChange={handleCloneAddress} disabled={isKHCT}>
                Sử dụng địa chỉ thường trú
              </Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName="rootAddress"
                    address={rootAddress}
                    handleAddressChange={validateForm}
                    isDisable={isKHCT || cloneAddress}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled={isKHCT || cloneAddress} />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán - Tối ưu hóa */}
            <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                {BankAccountList}
              </div>
            </Form.Item>

            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name="mainBankId"
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select
                  placeholder="Chọn tài khoản giao dịch chính"
                  options={bankOptions}
                  onChange={bankHandlers.selectMainBank}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                />
              </Form.Item>
              <Form.Item name="mainBank" hidden>
                <Input type="hidden" />
              </Form.Item>
            </div>
            <Title level={5} style={{ marginBottom: 16, marginTop: 16 }}>
              Thông tin vay
            </Title>
            <Form.Item style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
              <span style={{ minWidth: 120 }}>Vay ngân hàng</span>
              <Form.Item name="loanType" noStyle>
                <Radio.Group style={{ marginLeft: 16 }}>
                  <Radio value="yes">Có</Radio>
                  <Radio value="no">Không</Radio>
                </Radio.Group>
              </Form.Item>
            </Form.Item>
          </Col>
          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="description">
              <TextArea placeholder="Nhập ghi chú" maxLength={250} rows={4} />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath={type === 'business' ? 'deposit-request/business' : 'deposit-request/personal'}
                size={25}
              />
            </Form.Item>
          </Col>
          <Form.Item name="idBookingTicketCode" hidden>
            <Input type="hidden" />
          </Form.Item>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default CreateBookingRequestStep3;
