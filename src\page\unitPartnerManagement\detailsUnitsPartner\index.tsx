import { memo, useEffect } from 'react';
import { Button, Col, Form, Row, Tabs } from 'antd';
import type { TabsProps } from 'antd';
import AddFieldTab from './components/addFieldTab';
import './styles.scss';
import BreadCrumbComponent from '../../../components/breadCrumb';
import DetailsInfoTab from './components/detailsInfoTab';
import PersonnelManagementTab from './components/personnelManagementTab';
import LegalInfoTab from './components/legalInfoTab';
import PartnerManagementTab from './components/partnerManagementTab';
import { useParams } from 'react-router-dom';
import { useCreateField, useFetch, useUpdateField } from '../../../hooks';
import { UNIT_PARTNER_MANAGEMENT } from '../../../configs/path';
import { DetailsUnitsPartnerProvider } from './context/DetailsUnitsPartnerContext';
import { createUnitsPartner, getDetailUnitsPartner, updateUnitsPartner } from '../../../service/unitsPartner';
import { Partner } from '../../../types/unitsPartner/unitsPartner';

function DetailsUnitsPartner() {
  const { id } = useParams();
  const [form] = Form.useForm();
  const { data: detailUnitPartnerData } = useFetch<Partner>({
    api: () => id && getDetailUnitsPartner(id),
    queryKeyArr: id ? ['units-partner', id] : [],
    withFilter: false,
    enabled: !!id,
  });

  const create = useCreateField({
    keyOfListQuery: ['units-partner'],
    label: 'Đối tác hợp tác',
    apiQuery: createUnitsPartner,
    path: UNIT_PARTNER_MANAGEMENT,
  });

  const update = useUpdateField({
    keyOfListQuery: ['units-partner'],
    label: 'Đối tác hợp tác',
    apiQuery: updateUnitsPartner,
    path: UNIT_PARTNER_MANAGEMENT,
  });

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: 'Thông tin chi tiết',
      children: <DetailsInfoTab form={form} />,
    },
    {
      key: '2',
      label: 'Thông tin pháp lý',
      children: <LegalInfoTab form={form} />,
    },
    {
      key: '3',
      label: 'Danh sách đơn vị bán hàng đang hợp tác',
      children: <PartnerManagementTab partnerCode={detailUnitPartnerData?.data?.data?.partnerCode} />,
    },
    {
      key: '4',
      label: 'Quản lý nhân viên',
      children: <PersonnelManagementTab form={form} />,
    },
    {
      key: '5',
      label: 'Thêm thông tin trường đơn',
      children: <AddFieldTab form={form} />,
    },
  ];
  const onChange = (key: string) => {
    console.log(key);
  };

  const onUpdateUnitPartner = (values: Partner) => {
    console.log(values);
    const createUnitPartnerData = {
      ...values,
      partnershipName: values.partnershipName,
      taxCode: values.taxCode,
      partnershipCode: values.partnershipCode,
      partnershipLevel: '1',
      partnerCode: values.partnerCode,
      logo: '',
    };

    if (id) {
      update.mutate({ ...values, id: id });
    } else {
      create.mutate(createUnitPartnerData);
    }
  };

  useEffect(() => {
    if (detailUnitPartnerData?.data?.data && id) {
      const data = detailUnitPartnerData?.data?.data;
      form.setFieldsValue(data);
    }
  }, [detailUnitPartnerData, id, form]);

  const filteredItems = items.filter(item => item.key === '1');
  const _detailUnitPartnerData = detailUnitPartnerData?.data?.data || {
    partnershipLevel: '1',
    identityType: 'MST',
  };
  return (
    <div className={'box-detail-units'}>
      <DetailsUnitsPartnerProvider>
        <Form
          initialValues={_detailUnitPartnerData}
          form={form}
          name="dynamic_form_nest_item"
          layout="vertical"
          onFinish={values => onUpdateUnitPartner(values)}
        >
          <div className={'box-main-wrap'}>
            <BreadCrumbComponent titleBread={'Gamuda'} />
            <Tabs defaultActiveKey="1" items={id ? items : filteredItems} onChange={onChange} />
          </div>
          {/* Nút lưu */}
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item>
                <Button type="primary" htmlType="submit">
                  Lưu
                </Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </DetailsUnitsPartnerProvider>
    </div>
  );
}

export default memo(DetailsUnitsPartner);
