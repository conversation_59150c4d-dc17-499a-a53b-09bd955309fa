import React, { useState } from 'react';
import { Form, Select, Input, DatePicker, Button, Row, Col, Modal } from 'antd';
import DuplicateModal from './step2';
import { DuplicateResp, Employee, FormVerify } from '../../../../../types/bookingRequest';
import ModalComponent from '../../../../../components/modal';
import { useFetch } from '../../../../../hooks';
import { getIssuePlaces } from '../../../../../service/address';
import { getCustomerByIdentities, getEmployees } from '../../../../../service/bookingTicket';
import { AddressType } from '../../../../../components/selectAddress';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { getListBookingApproved } from '../../../../../service/depositRequired';
import { useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { FORMAT_DATE } from '../../../../../constants/common';
import { checkCustomerOrDemand } from '../../../../../service/transaction';
import { BookingTicketType } from '../../../../../types/transaction';
import { useProjectStore } from '../../../store';

const { Option } = Select;

interface CreateBookingRequestStep1Props {
  visible: boolean;
  onCancel: (hasFormChanged?: boolean, resetForm?: () => void) => void;
  onNext: (values: FormVerify, duplicateData?: DuplicateResp | null) => void;
}

const CreateBookingRequestStep1: React.FC<CreateBookingRequestStep1Props> = ({ visible, onCancel, onNext }) => {
  const { id: projectId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [duplicateModalVisible, setDuplicateModalVisible] = useState<boolean>(false);
  const [duplicateData, setDuplicateData] = useState<DuplicateResp | null>(null);
  const [customerType, setCustomerType] = useState<string>('individual'); // Mặc định là individual
  const [isCustomer, setIsCustomer] = useState<boolean>(false);

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-issue-places'],
    api: getIssuePlaces,
  });
  const provinces = dataProvinces?.data?.data;
  const { setEmployeeInfo } = useProjectStore();

  const checkDuplicateIdentity = async (
    type?: string,
    identityType?: string,
    identityNo?: string,
  ): Promise<DuplicateResp> => {
    try {
      // Kiểm tra KHCT (official-customer-personal)
      const customerResp = await getCustomerByIdentities({
        type: type,
        identityNo: identityNo,
        identityType: identityType,
      });
      const customerData = customerResp?.data?.data || [];
      if (customerData.length > 0) {
        return {
          isDuplicate: true,
          customerType: customerData[0]?.customerType,
          data: customerData.map((item: DuplicateResp) => ({
            ...item,
            type: type,
            customerType: item.customerType,
          })),
          type: type,
        };
      }

      // Kiểm tra KHTN (demand-customer-personal)
      const demandResp = await getCustomerByIdentities({
        type: type,
        identityNo: identityNo,
        identityType: identityType,
      });
      const demandData = demandResp?.data?.data || [];
      if (demandData.length > 0) {
        return {
          isDuplicate: true,
          customerType: demandData[0]?.customerType,
          data: demandData.map((item: DuplicateResp) => ({
            ...item,
            type: type,
            customerType: item?.customerType,
          })),
          type: type,
        };
      }

      return {
        isDuplicate: false,
        customerType: 'initial',
        type: type,
        data: [],
      };
    } catch (error) {
      return {
        isDuplicate: false,
        customerType: 'initial',
        type: type,
        data: [],
      };
    }
  };

  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      const { type, identityType, identityNo, taxCode } = values;

      const duplicateCheck = await checkDuplicateIdentity(
        type,
        identityType,
        type === 'business' ? taxCode : identityNo,
      );

      if (duplicateCheck.isDuplicate) {
        setDuplicateData(duplicateCheck);
        setDuplicateModalVisible(true);
      } else {
        onNext(values, duplicateCheck); // Không trùng, chuyển sang bước 3
      }
    } catch (errorInfo) {
      console.log('Validation failed:', errorInfo);
    }
  };

  const handleDuplicateConfirm = (selectedCustomer: unknown) => {
    setDuplicateModalVisible(false);
    const updatedDuplicateData = duplicateData
      ? { ...duplicateData, data: selectedCustomer ? [selectedCustomer] : duplicateData.data }
      : null;
    onNext(form.getFieldsValue(), updatedDuplicateData);
  };

  const handleDuplicateCancel = () => {
    Modal.confirm({
      title: 'Xác nhận hủy',
      content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
      cancelText: 'Quay lại',
      okText: 'Đồng ý',
      onOk: () => {
        setDuplicateModalVisible(false);
      },
      okButtonProps: {
        type: 'default',
      },
      cancelButtonProps: {
        type: 'primary',
      },
    });
  };

  const handleCancel = () => {
    onCancel(form.isFieldsTouched(), () => form.resetFields());
  };

  // Cập nhật identityType và reset các field liên quan khi type thay đổi
  const handleCustomerTypeChange = (value: string) => {
    setCustomerType(value);
    form.resetFields(['identityType', 'identityNo', 'taxCode', 'issueDate', 'issueLocation']);
  };

  const handleSelectBookingTicketCode = async (value: BookingTicketType) => {
    const identity = value?.customer?.personalInfo?.identities?.[0];

    form.validateFields(['bookingTicketCode']);
    setCustomerType(value?.customer?.type || 'individual');
    if (value?.customer) {
      const customerData = value.customer;

      let issueLocationValue = null;

      if (customerData.type === 'business' && customerData?.company?.issueLocation) {
        const issueLocation = customerData.company.issueLocation;
        if (typeof issueLocation === 'string') {
          // Tìm province dựa trên code hoặc nameVN
          const province = provinces?.find(p => p.code === issueLocation || p.nameVN === issueLocation);
          issueLocationValue = province ? { value: province.code, label: province.nameVN } : null;
        } else if (typeof issueLocation === 'object' && issueLocation.code) {
          // Nếu đã là object
          issueLocationValue = { value: issueLocation.code, label: issueLocation.nameVN };
        }
      } else if (identity?.place) {
        const place = identity.place;
        if (typeof place === 'string') {
          // Tìm province dựa trên code hoặc nameVN
          const province = provinces?.find(p => p.code === place || p.nameVN === place);
          issueLocationValue = province ? { value: province.code, label: province.nameVN } : null;
        } else if (typeof place === 'object' && place.code) {
          // Nếu đã là object
          issueLocationValue = { value: place.code, label: place.nameVN };
        }
      }

      form.setFieldsValue({
        type: customerData.type,
        identityType: customerData?.identityType,
        identityNo: identity?.value,
        taxCode: customerData?.taxCode,
        issueDate:
          customerData.type === 'business'
            ? customerData?.company?.issueDate
              ? dayjs(customerData?.company?.issueDate, FORMAT_DATE)
              : null
            : identity?.date
              ? dayjs(identity?.date, FORMAT_DATE)
              : null,
        issueLocation: issueLocationValue,
      });
    }
    try {
      const customerData = value.customer;
      const identityNumber = customerData?.type === 'business' ? customerData?.taxCode || '' : identity?.value || '';
      const identityType = customerData?.identityType || '';

      if (identityNumber && identityType) {
        const checkCustomerOrDemandResp = await checkCustomerOrDemand({
          identityNumber: identityNumber,
          identityType: identityType,
        });

        const checkCustomerOrDemandData = checkCustomerOrDemandResp?.data?.data;

        if (checkCustomerOrDemandData) {
          setIsCustomer(checkCustomerOrDemandData.isCustomer || false);
        } else {
          setIsCustomer(false);
        }
      }
    } catch (error) {
      setIsCustomer(false);
    }
    form.setFieldsValue({
      bookingTicketCode: value,
    });
  };

  const handleSelectEmployee = (value: Employee) => {
    form.validateFields(['employeeTakeCareId']);
    form.setFieldsValue({
      employeeTakeCareId: value,
    });
    setEmployeeInfo({
      id: value.id,
      name: value.name,
      code: value.code,
      orgCode: value.orgCode,
      orgName: value.orgName,
      accountId: value.accountId,
    });
  };

  return (
    <>
      <ModalComponent
        title="Tạo mới yêu cầu đặt cọc 1/3"
        open={visible}
        onCancel={handleCancel}
        destroyOnClose
        footer={
          <Button type="primary" htmlType="submit" onClick={handleNext}>
            Tiếp tục
          </Button>
        }
      >
        <Form form={form} layout="vertical" initialValues={{ type: 'individual' }}>
          <Row>
            <Col span={6}>
              <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
                <SingleSelectLazy
                  moreParams={{ isActive: true }}
                  apiQuery={getEmployees}
                  queryKey={['get-list-employee']}
                  keysLabel={['code', 'name']}
                  placeholder="Chọn mã nhân viên chăm sóc"
                  handleSelect={handleSelectEmployee}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item label="Mã phiếu YCDCHO/YCDCO" name="bookingTicketCode">
                <SingleSelectLazy
                  apiQuery={getListBookingApproved}
                  moreParams={{ idProject: projectId }}
                  queryKey={['get-request-deposit']}
                  keysLabel={['bookingTicketCode', 'escrowTicketCode']}
                  placeholder="Chọn mã YCDCHO/YCDCO"
                  handleSelect={handleSelectBookingTicketCode}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={6}>
              <Form.Item
                label="Loại khách hàng"
                name="type"
                rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng' }]}
              >
                <Select placeholder="Cá nhân" onChange={handleCustomerTypeChange} disabled={isCustomer}>
                  <Option value="individual">Khách hàng cá nhân</Option>
                  <Option value="business">Khách hàng doanh nghiệp</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <p style={{ marginBottom: 8 }}>
            Giấy tờ xác minh<span style={{ color: 'red' }}>*</span>
          </p>
          <Row
            gutter={[8, 8]}
            style={{
              backgroundColor: 'rgba(0, 0, 0, 0.02)',
              border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
              padding: 20,
              margin: 0,
            }}
          >
            <Col span={6}>
              <Form.Item
                label="Loại giấy tờ"
                name="identityType"
                rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
              >
                <Select placeholder="Chọn loại giấy tờ" disabled={isCustomer}>
                  {customerType === 'individual' ? (
                    <>
                      <Option value="CCCD">Căn cước công dân</Option>
                      <Option value="CMND">CMT</Option>
                      <Option value="Hộ chiếu">Hộ chiếu</Option>
                    </>
                  ) : (
                    <Option value="MST">Mã số thuế</Option>
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Số giấy tờ"
                name={customerType === 'business' ? 'taxCode' : 'identityNo'}
                rules={[
                  {
                    required: true,
                    message: 'Vui lòng nhập số giấy tờ',
                  },
                ]}
              >
                <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled={isCustomer} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item
                label="Ngày cấp"
                name="issueDate"
                rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
              >
                <DatePicker
                  placeholder="Chọn ngày cấp"
                  format="DD/MM/YYYY"
                  disabled={isCustomer}
                  disabledDate={current => current && current > dayjs().endOf('day')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label="Nơi cấp"
                name="issueLocation"
                rules={[{ required: true, message: 'Vui lòng chọn nơi cấp' }]}
              >
                <Select
                  disabled={isCustomer}
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  allowClear
                  options={provinces?.map(item => ({
                    value: item.code,
                    label: item.nameVN,
                  }))}
                  labelInValue
                  showSearch
                  placeholder="Chọn nơi cấp"
                  onChange={value => {
                    form.setFieldsValue({
                      issueLocation: value || undefined,
                    });
                  }}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </ModalComponent>

      <DuplicateModal
        visible={duplicateModalVisible}
        duplicateData={duplicateData}
        onConfirm={handleDuplicateConfirm}
        onCancel={handleDuplicateCancel}
      />
    </>
  );
};

export default CreateBookingRequestStep1;
