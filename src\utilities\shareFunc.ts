import { FormInstance } from 'antd/es/form';
import BigNumber from 'bignumber.js';
import dayjs, { Dayjs } from 'dayjs';
import get from 'lodash/get';
import { FORMAT_DATE_API, STATUS_ADJUSTMENT_VERSION } from '../constants/common';
import { AnyObject } from 'antd/es/_util/type';

// Hàm kiểm tra object có ít nhất một giá trị không rỗng
export const hasNonEmptyValues = (obj?: Record<string, unknown>): boolean => {
  return (
    !!obj &&
    Object.values(obj).some(value => {
      if (typeof value === 'string') return value.trim() !== '';
      if (Array.isArray(value)) return value.length > 0;
      if (value && typeof value === 'object') return Object.keys(value).length > 0;
      return !!value;
    })
  );
};

// hàm kiểm tra URL hợp lệ
export function validateProfileUrl(url: string) {
  if (!url) {
    return { isValid: true };
  }

  // Regex để kiểm tra URL hợp lệ
  const urlPattern = /^(https?:\/\/)?([\w\d-]+\.)+[\w\d]{2,}(\/[/\w\d-]*)*\/?$/;

  if (url.includes(' ')) {
    return {
      isValid: false,
      message: 'URL không được chứa khoảng trắng',
    };
  }

  if (!urlPattern.test(url)) {
    return {
      isValid: false,
      message: 'URL không hợp lệ. Vui lòng nhập URL đúng định dạng (ví dụ: https://example.com)',
    };
  }

  return { isValid: true };
}

// hàm kiểm tra email hợp lệ
export function validateEmail(email: string) {
  if (!email) return Promise.resolve();
  const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  return emailPattern.test(email) ? Promise.resolve() : Promise.reject('Địa chỉ email sai định dạng');
}

// Hàm định dạng số thành tiền tệ VND
export const formatCurrency = (value: string, priceType?: string): string => {
  if (!value) return '';

  const numberValue = parseInt(value.toString().replace(/\D/g, ''), 10);
  if (isNaN(numberValue)) return '';

  return numberValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + `${priceType ? ' ' + priceType : ''}`; // Thêm đơn vị tiền tệ
};

export const formatRangePikerToSubmit = (date: Dayjs[]) => {
  if (!date) return null;
  return { startDate: date[0].format(FORMAT_DATE_API), endDate: date[1].format(FORMAT_DATE_API) };
};

// Hàm tải file từ ArrayBuffer
export function downloadArrayBufferFile(props: { data: ArrayBuffer; fileName: string; type?: string }) {
  const { data, fileName, type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' } = props;
  try {
    // Tạo Blob từ ArrayBuffer
    const blob = new Blob([data], { type });

    // Tạo URL và tải file
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = fileName;
    document.body.appendChild(link);
    link.click();

    // Dọn dẹp
    URL.revokeObjectURL(link.href);
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading file:', error);
  }
}

export const hasNonNullValue = (addressObj: any): boolean => {
  // Nếu addressObj không tồn tại hoặc là null, trả về false
  if (!addressObj) return false;

  // Các trường bắt buộc phải kiểm tra
  const requiredFields = ['country', 'province', 'district', 'ward', 'address'];

  // Kiểm tra xem tất cả các trường bắt buộc có giá trị hợp lệ hay không
  return requiredFields.every(field => {
    if (field === 'address') {
      // Trường address là chuỗi, kiểm tra xem nó có tồn tại và không rỗng
      return addressObj[field] && addressObj[field].trim() !== '';
    } else {
      // Các trường country, province, district, ward là object, kiểm tra xem chúng có giá trị hợp lệ
      return (
        addressObj[field] &&
        typeof addressObj[field] === 'object' &&
        Object.values(addressObj[field]).some(value => value != null && value.toString().trim() !== '')
      );
    }
  });
};

export const formatBigNumber = (value: number | string | undefined): string => {
  if (value === null || value === undefined) return '';

  try {
    const bigNumberValue = new BigNumber(value);
    if (bigNumberValue.isNaN()) return '';

    return bigNumberValue.toFormat({
      decimalSeparator: '.',
      groupSeparator: ',',
      groupSize: 3,
      fractionGroupSeparator: '',
      fractionGroupSize: 0,
    });
  } catch (error) {
    console.error('Error formatting number:', error);
    return '';
  }
};

export const validateSpaceInput = (value: string) => {
  if (value.includes(' ')) {
    return {
      isValid: false,
      message: 'URL không được chứa khoảng trắng',
    };
  }
};

// Hàm chuyển đổi (vd:Tên sự kiện → ten-su-kien)
export const slugify = (value?: string) => {
  if (!value) return undefined;
  return value
    .replace(/đ/g, 'd') // chuyển đ -> d
    .replace(/Đ/g, 'D') // chuyển Đ -> D
    .normalize('NFD') // remove accents
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // replace spaces with hyphens
    .replace(/[^\w-]/g, '');
};

export const changeViewCount = (count: number | string): string => {
  const num = typeof count === 'string' ? Number(count) : count;
  if (isNaN(num)) return String(count);

  if (num >= 1_000_000_000) return `${(num / 1_000_000_000).toFixed(1)}B`;
  if (num >= 1_000_000) return `${(num / 1_000_000).toFixed(1)}M`;
  if (num >= 1_000) return `${(num / 1_000).toFixed(1)}K`;

  return String(num);
};

export function timeAgo(dateString?: string) {
  const now = new Date();
  const date = dateString ? new Date(dateString) : new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  const units = [
    { name: 'năm', value: 365 * 24 * 60 * 60 },
    { name: 'tháng', value: 30 * 24 * 60 * 60 },
    { name: 'tuần', value: 7 * 24 * 60 * 60 },
    { name: 'ngày', value: 24 * 60 * 60 },
    { name: 'giờ', value: 60 * 60 },
    { name: 'phút', value: 60 },
    { name: 'giây', value: 1 },
  ];

  for (const unit of units) {
    const amount = Math.floor(diffInSeconds / unit.value);
    if (amount >= 1) {
      return `${amount} ${unit.name}${amount > 1 ? '' : ''}`;
    }
  }
  return 'vừa xong';
}

export function getFullAddress(addressObj: any): string {
  const addressParts = [
    addressObj?.address?.trim() || '',
    addressObj?.ward?.name?.trim() || '',
    addressObj?.district?.name?.trim() || '',
    addressObj?.province?.name?.trim() || '',
  ].filter(part => part.length > 0);

  return addressParts.length > 0 ? addressParts.join(', ') : '';
}
export const formatPercent = (value: string, isDefault?: boolean): string => {
  if (!value && !!isDefault) return '0';

  let formattedValue = value;

  // Xử lý các trường hợp đặc biệt
  if (formattedValue === '.') {
    formattedValue = '0.00';
  } else if (formattedValue.toString().startsWith('.')) {
    formattedValue = '0' + formattedValue;
  }

  // Thêm phần thập phân nếu cần
  if (formattedValue.toString().includes('.')) {
    // Đảm bảo có đúng 2 chữ số sau dấu chấm
    const parts = formattedValue.toString().split('.');
    formattedValue = parts[0] + '.' + (parts[1] + '00').toString().slice(0, 2);
  } else if (formattedValue !== '' && formattedValue !== '0') {
    // Nếu không có phần thập phân, thêm .00
    formattedValue = formattedValue + '.00';
  }

  return formattedValue;
};

// Hàm validate ngày kết thúc >= ngày bắt đầu
export const validateEndDate =
  (form: FormInstance, startField: string, errorMessage: string) => (_: unknown, value: Dayjs | null) => {
    const startDate = form.getFieldValue(startField) as Dayjs | undefined;
    if (value && startDate && dayjs(value).isBefore(startDate, 'day')) {
      return Promise.reject(errorMessage);
    }
    return Promise.resolve();
  };

export const filterData = <T extends AnyObject>(data: T[], keyword: string = '', fields: string[]) => {
  if (!keyword.trim()) return data;
  const lowerKeyword = keyword.toLowerCase();
  return data.filter(item => {
    return fields.some(field => {
      const nestedObj = get(item, field);

      if (typeof nestedObj === 'string') {
        return nestedObj.toLowerCase().includes(lowerKeyword);
      }

      if (!nestedObj || typeof nestedObj !== 'object') return false;
      return Object.values(nestedObj).some(val => val.toString().toLowerCase().includes(lowerKeyword));
    });
  });
};

export const checkStatusAdjustmentVersion = (value: string) => {
  return (
    value === STATUS_ADJUSTMENT_VERSION.APPROVED ||
    value === STATUS_ADJUSTMENT_VERSION.ANNOUNCED ||
    value === STATUS_ADJUSTMENT_VERSION.WAITING
  );
};

export const handleDownloadFileS3 = async (fileUrl: string, fileName: string) => {
  const response = await fetch(fileUrl);
  const blob = await response.blob();
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${fileName}`;
  link.click();
  window.URL.revokeObjectURL(url);
};

export const checkUrl = (url: string): boolean => {
  const regex = /^https?:\/\//;
  return regex.test(url);
};
