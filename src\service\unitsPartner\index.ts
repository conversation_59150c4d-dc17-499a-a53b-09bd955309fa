import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ListCooperativeSalesUnits, Partner } from '../../types/unitsPartner/unitsPartner';

export const getListUnitsPartner = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/findAllByQuery`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailUnitsPartner = async (id: string) => {
  const response = await getRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/${id}`,
  );
  return response;
};

export const createUnitsPartner = async (payload: Partner) => {
  const response = await postRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.domain}/external/orgchart`,
    payload,
  );
  return response;
};

export const deleteUnitsPartner = async (payload: unknown) => {
  const response = await deleteRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.domain}/external/orgchart/${payload?.id}`,
    payload as Record<string, unknown> | undefined,
  );
  return response;
};

export const updateUnitsPartner = async (payload: Partner) => {
  const response = await putRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.domain}/external/orgchart`,
    payload,
  );
  return response;
};

export const getListOfCooperativeSalesUnits = async (payload: ListCooperativeSalesUnits) => {
  const response = await postRequest(
    `${urlDomainApi.msx_orgChart}/${typeQueryVersionApi.query}/external/orgchart/listOfCooperativeSalesUnits`,
    payload,
  );
  return response;
};
