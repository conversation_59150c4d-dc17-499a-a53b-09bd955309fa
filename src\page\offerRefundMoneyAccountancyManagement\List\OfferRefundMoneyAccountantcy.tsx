import { <PERSON>ton, Col, Row, TableColumnsType, Typography } from 'antd';
import TableComponent from '../../../components/table';
import { useStoreOfferRefundMoneyAccountancy } from '../storeOfferRefundMoney';
import FilterSearch from '../component/FilterSearch';
import OfferModalComponent from '../component/offerModal';
import { IOfferRefundMoneyAccountancy, TabOfferRefundMoneyAccountancy } from '../../../types/offer';
import { useState } from 'react';
import './styles.scss';
import dayjs from 'dayjs';
import {
  EAPP_STATUS_COLOR,
  EAPP_STATUS_NAME,
  FORMAT_DATE_TIME,
  OFFER_STATUS_COLOR,
  OFFER_STATUS_NAME,
} from '../../../constants/common';
import { formatCurrency } from '../../../utilities/shareFunc';
import {
  Link,
  //  useNavigate
} from 'react-router-dom';
import { OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT } from '../../../configs/path';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import OfferCreateModal from '../component/createOfferModal';
import {
  deleteOfferRefund,
  // cancelOffer,
  sendProposal,
} from '../../../service/offer';
import ConfirmActionModal from '../../../components/modal/specials/ConfirmActionModal';
import { MutationFunction } from '@tanstack/react-query';
import { useCreateField } from '../../../hooks';

const { Text } = Typography;
interface OfferRefundMoneyAccountancyProps {
  tabActive: TabOfferRefundMoneyAccountancy;
}

const OfferRefundMoneyAccountancy = (props: OfferRefundMoneyAccountancyProps) => {
  // const navigate = useNavigate();
  const { tabActive } = props;
  const { dataOfferRefund, loading } = useStoreOfferRefundMoneyAccountancy();
  const [initialOffer, setInitialOffer] = useState<IOfferRefundMoneyAccountancy>();
  const [isOpenOfferModal, setOfferModal] = useState<boolean>(false);
  const [isCreateOpenOfferModal, setCreateOfferModal] = useState<boolean>(false);
  const [isDeleteOfferModal, setDeleteOfferModal] = useState<boolean>(false);

  const { mutateAsync: _sendProposal } = useCreateField<IOfferRefundMoneyAccountancy>({
    apiQuery: sendProposal,
    isMessageError: false,
  });

  const columns: TableColumnsType<IOfferRefundMoneyAccountancy> = [
    {
      title: 'Mã tờ trình',
      dataIndex: 'eappNumber',
      key: 'eappNumber',
      width: 110,
      render: (value: string, record?: IOfferRefundMoneyAccountancy) => {
        return record?.urlEapp ? (
          <Link to={`${record?.urlEapp}`} target="_blank">
            {value || ''}
          </Link>
        ) : (
          <Text>{value || ''}</Text>
        );
      },
    },
    {
      title: 'Mã đề nghị',
      dataIndex: 'code',
      key: 'code',
      width: 110,
      render: (value: string, record?: IOfferRefundMoneyAccountancy) => (
        <Link to={`${OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT}/${record?.id}`}>{value || ''}</Link>
      ),
    },

    {
      title: 'Tên khách hàng',
      dataIndex: 'customer',
      key: 'customer',
      width: 153,
      render: (_: string, record: IOfferRefundMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.customer?.type === 'business'
            ? record?.propertyTicket?.customer?.company?.name
            : record?.propertyTicket?.customer?.personalInfo?.name || ''}
        </Text>
      ),
    },
    {
      title: 'Đơn vị bán hàng',
      dataIndex: 'pos',
      key: 'pos',
      width: 183,
      render: (_: string, record: IOfferRefundMoneyAccountancy) => <Text>{record?.pos?.name || ''}</Text>,
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 106,
      render: (value: string) => <Text>{dayjs(value).format(FORMAT_DATE_TIME) || ''}</Text>,
    },
    {
      title: 'Mã YC/HĐ',
      dataIndex: 'bookingTicketCode',
      key: 'bookingTicketCode',
      width: 145,
      render: (_: string, record: IOfferRefundMoneyAccountancy) => (
        <Text>
          {record?.propertyTicket?.ticketType === 'YCDCH'
            ? record?.propertyTicket?.bookingTicketCode
            : record?.propertyTicket?.escrowTicketCode || ''}
        </Text>
      ),
    },
    {
      title: 'Mã chứng từ',
      dataIndex: 'receiptNum',
      key: 'receiptNum',
      width: 132,
      render: (_: string, record: IOfferRefundMoneyAccountancy) => <Text>{record?.receiptNum || ''}</Text>,
    },
    {
      title: 'Số SP',
      dataIndex: 'propertyUnit',
      key: 'propertyUnit',
      width: 139,
      render: (_: string, record: IOfferRefundMoneyAccountancy) => (
        <Text>{record?.propertyTicket?.propertyUnit?.code || ''}</Text>
      ),
    },

    {
      title: 'Trạng thái duyệt',
      dataIndex: 'ticketStatus',
      key: 'ticketStatus',
      align: 'center',
      width: 150,
      render: (value: string) => {
        return <Text style={{ color: EAPP_STATUS_COLOR[value] }}>{EAPP_STATUS_NAME[value]}</Text>;
      },
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      width: 150,
      render: (value: string) => (
        <Text style={{ color: OFFER_STATUS_COLOR[value] }}>{OFFER_STATUS_NAME[value] || ''}</Text>
      ),
    },
    {
      title: 'Số tiền',
      dataIndex: 'money',
      key: 'money',
      width: 149,
      align: 'center',
      render: (_: string, record: IOfferRefundMoneyAccountancy) => (
        <Text>{record?.money ? formatCurrency(record?.money.toString()) : ''}</Text>
      ),
    },
    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 80,
      render: (_: string, record: IOfferRefundMoneyAccountancy) => {
        // const handleViewDetail = () => {
        //   navigate(`${OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT}/${record?.id}`);
        // };
        const handlePrint = () => {};
        // const handleApprove = () => {
        //   setOfferModal(true);
        //   setInitialOffer(record);
        // };
        const handlePrintWT = () => {};
        const handleDelete = () => {
          setDeleteOfferModal(true);
          setInitialOffer(record);
        };

        const handleSendProposal = async () => {
          try {
            const resp = await _sendProposal({ id: record?.id });
            if (resp?.data?.statusCode === '0') {
              window.open((resp?.data?.data as { urlEapp?: string })?.urlEapp, '_blank', 'noopener,noreferrer');
            }
          } catch (error) {
            console.error('Error sending proposal:', error);
          }
        };
        return tabActive === 'WAITING_TRANSFER' ? (
          <ActionsColumns
            moreActions={[
              // {
              //   label: 'Duyệt',
              //   key: 'approve',
              //   onClick: handleApprove,
              // },
              record?.status === 'INITIAL'
                ? {
                    label: 'Gửi tờ trình',
                    key: 'sendProposal',
                    onClick: handleSendProposal,
                  }
                : null,
              {
                label: 'In',
                key: 'print',
                onClick: handlePrintWT,
              },
              record?.status === 'INITIAL'
                ? {
                    label: 'Xóa',
                    key: 'delete',
                    onClick: handleDelete,
                  }
                : null,
            ]}
          />
        ) : (
          <ActionsColumns
            // handleViewDetail={handleViewDetail}
            handlePrint={handlePrint}
          />
        );
      },
    },
  ];

  return (
    <>
      <div className="header-content" style={{ marginBottom: 16 }}>
        <FilterSearch />
        {tabActive === 'WAITING_TRANSFER' && (
          <Row gutter={[16, 8]}>
            <Col>
              <Button type="primary" onClick={() => setCreateOfferModal(true)}>
                Tạo mới
              </Button>
            </Col>
          </Row>
        )}
      </div>
      <TableComponent
        className="table-offer-refund"
        columns={columns}
        queryKeyArr={['get-offer-refund', tabActive]}
        dataSource={dataOfferRefund[tabActive]}
        loading={loading}
        rowKey={'id'}
      />
      <OfferModalComponent
        title="Đề nghị hoàn tiền"
        visible={isOpenOfferModal}
        onCancel={() => setOfferModal(false)}
        onOk={() => setOfferModal(false)}
        onValuesChange={() => {}}
        // initialValues={initialOffer}
      />
      <OfferCreateModal visible={isCreateOpenOfferModal} onClose={() => setCreateOfferModal(false)} />

      <ConfirmActionModal
        open={isDeleteOfferModal}
        apiQuery={deleteOfferRefund as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-offer-refund', tabActive]}
        onCancel={() => setDeleteOfferModal(false)}
        title="Xóa đề nghị hoàn tiền"
        description="Vui lòng nhập lý do xóa đề nghị hoàn tiền này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Xác nhận"
        fieldNameReason="reasonDelete"
        maxLength={255}
        disable={true}
        payload={{ id: initialOffer?.id }}
        showReasonField={true}
      />
    </>
  );
};

export default OfferRefundMoneyAccountancy;
